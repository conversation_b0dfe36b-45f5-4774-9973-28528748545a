# 🔑 Railway Deployment - Login Credentials

## ✅ **Fixed CPF Validation Issue**

The deployment was failing because the setup script was using invalid test CPFs. This has been fixed with valid CPFs.

## 🚀 **Deploy Steps:**

1. **Push the fixes:**
```bash
git add .
git commit -m "Fix CPF validation for Railway deployment"
git push origin main
```

2. **After successful deployment, create admin user:**
```bash
railway run python setup_admin.py
```

## 🔑 **Login Credentials (Valid CPFs):**

### 👨‍💼 **Administrator Account:**
- **CPF:** `***********`
- **Password:** `admin123`
- **Access:** Full admin access

### 👤 **Test Customer Account:**
- **CPF:** `***********`
- **Password:** `teste123`
- **Access:** Customer portal

## 🌐 **URLs to Test:**

- **Main Site:** https://web-production-b6ae1.up.railway.app/
- **Login Page:** https://web-production-b6ae1.up.railway.app/auth/login/
- **Admin Panel:** https://web-production-b6ae1.up.railway.app/admin/
- **New Ticket Form:** https://web-production-b6ae1.up.railway.app/suporte/novo/

## 🛠️ **Manual Setup (if needed):**

If the automatic setup doesn't work, you can manually create the admin user:

```bash
# Connect to Railway
railway login
railway link

# Create superuser manually
railway run python manage.py createsuperuser

# Or run the setup script
railway run python setup_admin.py
```

## ⚠️ **Important Notes:**

1. **CPF Format:** The system validates CPFs, so only use valid Brazilian CPF numbers
2. **First Login:** Change the default passwords after first login
3. **Admin Access:** Use the admin account to manage the system
4. **Customer Testing:** Use the test customer account to test the ticket system

## 🎯 **Next Steps:**

1. Deploy the fixes
2. Create admin user
3. Test the improved ticket creation form
4. Verify all functionality works correctly

The new ticket form includes:
- ✅ Multiple file uploads
- ✅ Custom subject creation
- ✅ Detailed problem information fields
- ✅ Technical information capture
- ✅ Responsive design
- ✅ Real-time validation
