# 🚀 Deploy Rápido no Railway

## Passo a Passo Simplificado

### 1. Preparação (Já feito!)
✅ Procfile criado
✅ runtime.txt criado  
✅ requirements.txt atualizado
✅ settings.py configurado para produção

### 2. Deploy no Railway

#### Opção A: Via GitHub (Recomendado)
1. **Fazer push para GitHub:**
```bash
git add .
git commit -m "Preparar para deploy no Railway"
git push origin main
```

2. **Acessar Railway:**
   - Vá para: https://railway.app
   - Clique em "Start a New Project"
   - Selecione "Deploy from GitHub repo"
   - Escolha seu repositório

3. **Configurar variáveis de ambiente:**
   No painel do Railway, vá em "Variables" e adicione:
   ```
   SECRET_KEY = django-insecure-GERE-UMA-NOVA-CHAVE-AQUI
   DEBUG = False
   ALLOWED_HOSTS = *.railway.app
   ```

#### Opção B: Via Railway CLI
1. **Instalar Railway CLI:**
```bash
npm install -g @railway/cli
```

2. **Login e deploy:**
```bash
railway login
railway init
railway up
```

### 3. Configurar Banco de Dados
1. No painel Railway, clique em "Add Service"
2. Selecione "PostgreSQL"
3. Railway conecta automaticamente!

### 4. Executar migrações
No terminal do Railway ou localmente:
```bash
railway run python manage.py migrate
railway run python manage.py collectstatic --noinput
railway run python manage.py createsuperuser
```

### 5. Acessar sua aplicação
URL será algo como: `https://seu-projeto-production.railway.app`

---

## 🔧 Troubleshooting

### Erro de ALLOWED_HOSTS:
Adicione o domínio do Railway nas variáveis:
```
ALLOWED_HOSTS = *.railway.app,seu-dominio.railway.app
```

### Erro de arquivos estáticos:
Execute:
```bash
railway run python manage.py collectstatic --noinput
```

### Erro de banco de dados:
Verifique se o PostgreSQL está conectado e execute:
```bash
railway run python manage.py migrate
```

---

## 📱 Testando

Após o deploy, teste:
1. **Login:** `/auth/login/`
2. **Dashboard:** `/dashboard/`
3. **Novo Chamado:** `/suporte/novo/`
4. **Admin:** `/admin/`

---

## 🎯 URL Final
Sua aplicação estará disponível em:
`https://[nome-do-projeto].railway.app`

**Tempo estimado:** 5-10 minutos! 🚀
