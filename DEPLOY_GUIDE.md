# 🚀 Guia de Deploy - Portal do Provedor

## Opções de Deploy para Testes

### 1. 🆓 **Railway** (Recomendado - <PERSON><PERSON>)

**Vantagens:** Deploy automático via Git, PostgreSQL gratuito, SSL automático
**Custo:** Gratuito (500 horas/mês)

#### Passos:
1. **Criar conta:** https://railway.app
2. **Conectar GitHub:** Faça push do projeto para GitHub
3. **Deploy automático:** Railway detecta Django automaticamente

#### Arquivos necessários:
```bash
# Criar Procfile
echo "web: gunicorn provedor.wsgi --log-file -" > Procfile

# Atualizar requirements.txt
echo "gunicorn==21.2.0" >> requirements.txt
echo "whitenoise==6.6.0" >> requirements.txt
```

#### Configurações Railway:
- **PORT:** 8000
- **DATABASE_URL:** (automático)
- **SECRET_KEY:** (gerar nova)
- **DEBUG:** False
- **ALLOWED_HOSTS:** *.railway.app

---

### 2. 🌐 **Render** (Fácil e Confiável)

**Vantagens:** PostgreSQL gratuito, SSL automático, fácil configuração
**Custo:** Gratuito (com limitações)

#### Passos:
1. **Criar conta:** https://render.com
2. **Conectar repositório GitHub**
3. **Configurar Web Service**

#### Configurações Render:
- **Build Command:** `pip install -r requirements.txt && python manage.py collectstatic --noinput && python manage.py migrate`
- **Start Command:** `gunicorn provedor.wsgi:application`
- **Environment:** Python 3

---

### 3. ☁️ **Heroku** (Clássico)

**Vantagens:** Muito documentado, add-ons fáceis
**Custo:** $5-7/mês (não tem plano gratuito mais)

#### Passos:
1. **Instalar Heroku CLI:** https://devcenter.heroku.com/articles/heroku-cli
2. **Login:** `heroku login`
3. **Criar app:** `heroku create seu-app-name`
4. **Deploy:** `git push heroku main`

---

### 4. 🐳 **DigitalOcean App Platform**

**Vantagens:** Boa performance, fácil escalar
**Custo:** $5/mês

#### Configuração:
- **Source:** GitHub
- **Type:** Web Service
- **Environment:** Python

---

### 5. 💻 **PythonAnywhere** (Específico para Python)

**Vantagens:** Especializado em Python/Django
**Custo:** Gratuito (com limitações) ou $5/mês

#### Passos:
1. **Criar conta:** https://www.pythonanywhere.com
2. **Upload código via Git**
3. **Configurar Web App**

---

## 📋 Preparação do Projeto

### 1. Criar arquivos de deploy:

#### `Procfile`:
```
web: gunicorn provedor.wsgi --log-file -
```

#### `runtime.txt`:
```
python-3.11.0
```

#### Atualizar `requirements.txt`:
```
Django==4.2.7
djangorestframework==3.14.0
requests==2.32.3
python-decouple==3.8
psycopg2-binary==2.9.10
celery==5.3.6
redis==6.2.0
firebase-admin==6.9.0
Pillow==11.2.1
django-cors-headers==4.3.1
django-extensions==3.2.3
gunicorn==21.2.0
whitenoise==6.6.0
```

### 2. Configurar settings para produção:

#### `provedor/settings.py` (adicionar):
```python
import os
import dj_database_url

# Whitenoise para arquivos estáticos
MIDDLEWARE.insert(1, 'whitenoise.middleware.WhiteNoiseMiddleware')

# Database para produção
if 'DATABASE_URL' in os.environ:
    DATABASES['default'] = dj_database_url.parse(os.environ['DATABASE_URL'])

# Arquivos estáticos
STATIC_ROOT = os.path.join(BASE_DIR, 'staticfiles')
STATICFILES_STORAGE = 'whitenoise.storage.CompressedManifestStaticFilesStorage'

# Media files
MEDIA_ROOT = os.path.join(BASE_DIR, 'media')
MEDIA_URL = '/media/'

# Security
if not DEBUG:
    SECURE_SSL_REDIRECT = True
    SECURE_PROXY_SSL_HEADER = ('HTTP_X_FORWARDED_PROTO', 'https')
```

### 3. Criar `.env` para produção:
```
SECRET_KEY=sua-chave-secreta-super-segura
DEBUG=False
ALLOWED_HOSTS=seu-dominio.com,*.railway.app,*.render.com
DATABASE_URL=postgres://user:pass@host:port/db
```

---

## 🎯 Recomendação: Railway (Mais Rápido)

### Passo a passo Railway:

1. **Preparar projeto:**
```bash
# Adicionar arquivos de deploy
echo "web: gunicorn provedor.wsgi --log-file -" > Procfile
echo "python-3.11.0" > runtime.txt
echo "gunicorn==21.2.0" >> requirements.txt
echo "whitenoise==6.6.0" >> requirements.txt
echo "dj-database-url==2.1.0" >> requirements.txt
```

2. **Fazer push para GitHub:**
```bash
git add .
git commit -m "Preparar para deploy"
git push origin main
```

3. **Deploy no Railway:**
   - Acesse https://railway.app
   - Clique "Deploy from GitHub"
   - Selecione seu repositório
   - Railway faz deploy automático!

4. **Configurar variáveis:**
   - `SECRET_KEY`: gerar nova chave
   - `DEBUG`: False
   - `ALLOWED_HOSTS`: *.railway.app

### URL final:
`https://seu-projeto.railway.app`

---

## 🧪 Teste Rápido Local

Para testar antes do deploy:
```bash
# Simular produção localmente
python manage.py collectstatic --noinput
gunicorn provedor.wsgi:application
```

---

## 📞 Suporte

Se precisar de ajuda com alguma plataforma específica, me avise qual você escolheu e posso ajudar com os detalhes!

**Recomendação:** Comece com Railway - é o mais fácil e rápido para testar! 🚀
